<?php 

require_once 'DatabaseConnection.php';

try {
    session_start();
    $db = DatabaseConnection::getInstance();
    $conex = $db->getConnection();
    
    // Guardar usuario en sesión después del login exitoso
    if (isset($userRut)) { // RUT del usuario
        $_SESSION['usuario'] = $userRut;
    }
    
    register_shutdown_function(function() use ($db) {
        if ($db !== null) {
            $db->cleanup(); 
        }
    });

} catch (Exception $e) {
    error_log("Error de conexión: " . $e->getMessage());
    die("Error de conexión: Por favor, contacte al administrador");
}

?>


<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">
  <meta name="description" content="APP-TQW">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <!-- The above 4 meta tags *must* come first in the head; any other head content must come *after* these tags -->


    <title>PLATAFORMA TQW </title>

   
  </head>


<body class="hold-transition sidebar-mini">



<?php


if (isset($_POST["e_mail"]) && isset($_POST["passs"])) {
    $correo = $_POST["e_mail"];
    $clave = $_POST["passs"];
} 
// print($_POST["passs"]);
// print($_POST["e_mail"]);

// if ($inc) {
//     echo "<p>Conexión exitosa a la base de datos.</p>";
//   } else {
//     echo "<p>Error al conectar a la base de datos.</p>";
//   }


if (isset($_GET['correo'])) {

  $correo = $_GET['correo'];
  $clave = $_GET["clave"];
  $rut = $_GET["rut"];

$resultado = $conex->query(

"select t.* , w.nombre 
, w.area , w.supervisor 
, w.iden_user , w.rut 
, w.PERFIL
FROM  (
	SELECT  
    a.usuario , a.pass_new, a.fecha_registro 
	, count(*) total
	FROM  
		TB_CLAVES_USUARIOS a
	LEFT JOIN  
		TB_CLAVES_USUARIOS b
	ON a.usuario = b.usuario 
	AND a.fecha_registro <= b.fecha_registro
	GROUP BY 
    a.usuario , a.pass_new , a.fecha_registro
) t
LEFT JOIN 
	tb_user_tqw w 
ON t.usuario = w.email 
WHERE  t.total = 1 
and t.usuario ='".$correo."'");




}
else {




  $resultado = $conex->query(
      "select t.* , w.nombre 
      , w.area , w.supervisor 
      , w.iden_user , w.rut 
      , w.PERFIL
      FROM  (
        SELECT  
          a.usuario , a.pass_new, a.fecha_registro 
        , count(*) total
        FROM  
          TB_CLAVES_USUARIOS a
        LEFT JOIN  
          TB_CLAVES_USUARIOS b
        ON a.usuario = b.usuario 
        AND a.fecha_registro <= b.fecha_registro
        GROUP BY 
          a.usuario , a.pass_new , a.fecha_registro
      ) t
      LEFT JOIN 
        tb_user_tqw w 
      ON t.usuario = w.email 
      WHERE  t.total = 1 
      and vigente = 'Si'
      and t.usuario ='".$correo."'
      and pass_new = '".$clave."'"
);



}



while ($row = mysqli_fetch_array($resultado))
{

	$userEmail = $row['usuario'];
	$userPassword = $row['pass_new'];
  $userName = $row['nombre'];
  $userArea = $row['area'];
  $userSupervisor = $row['supervisor'];
  $userId = $row['iden_user'];
  $userRut = $row['rut'];

  $userProfile = $row['PERFIL'];

}

if ( $userEmail == $correo and $userPassword == $clave) 
{

      // Generar una clave temporal (aquí se utiliza una clave aleatoria de 8 caracteres)
    $clave_temporal = substr(str_shuffle('0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ'), 0, 12);


      if (isset($_GET['correo']))
      {

          // Realizar la consulta SQL para insertar los datos en la tabla
      $sql = "INSERT INTO TB_LOG_APP
      VALUES ('$userRut',now(), '$clave_temporal','NO')";

      }
      
      else 
      {
      // Realizar la consulta SQL para insertar los datos en la tabla
      $sql = "INSERT INTO TB_LOG_APP
      VALUES ('$userRut',now(), '$clave_temporal','SI')";
      }

      // Ejecutar la consulta
      if (mysqli_query($conex, $sql)) {
      } else {
      }
}


if ( $userEmail == $correo and $userPassword == $clave ) 

{
	
  
  if ($userEmail == "<EMAIL>" || $userEmail == "<EMAIL>") 
    
  {
    
    $redirectTo = "new_dash.php?id_sesion=" . urlencode($clave_temporal);
    ?>

    <script type="text/javascript">
    window.location = '<?php echo $redirectTo; ?>';
    </script>

    <?php    
  }


  if ($userProfile == "user_QA") 
    
  {
    // Establecer variables de sesión
    $_SESSION['periodo'] = "202505"; // Periodo estático
    $_SESSION['rut'] = $userRut;
    $_SESSION['id_sesion'] = $clave_temporal;
    
    $redirectTo = "mod_logistica.php";
    ?>

    <script type="text/javascript">
    window.location = '<?php echo $redirectTo; ?>';
    </script>

    <?php    
  }

  if ($userProfile == "TECNICO REDES") 
    
  {
    // Establecer variables de sesión
    $_SESSION['periodo'] = "202505"; // Periodo estático
    $_SESSION['rut'] = $userRut;
    $_SESSION['id_sesion'] = $clave_temporal;
    
    $redirectTo = "activity_dashboard.php";
    ?>

    <script type="text/javascript">
    window.location = '<?php echo $redirectTo; ?>';
    </script>

    <?php    
  }

  if ($userArea == "simomaco") 
    
  {
    
    $redirectTo = "Simomaco_ticket.php?id_sesion=" . urlencode($clave_temporal);
    ?>

    <script type="text/javascript">
    window.location = '<?php echo $redirectTo; ?>';
    </script>

    <?php    
  }


    if ($userArea == "admin")     
    {    
      $redirectTo = "new_dash.php?id_sesion=" . urlencode($clave_temporal);
      ?>

      <script type="text/javascript">
      window.location = '<?php echo $redirectTo; ?>';
      </script>

      <?php    
    }

    

    
    if ($userArea == "bodega") 
    
    {
          

      // Redirigir en función del perfil del usuario y agregar la clave temporal como parámetro GET
      $redirectTo = "home_bodega_new.php?id_sesion=" . urlencode($clave_temporal);
      ?>

      <script type="text/javascript">
      window.location = '<?php echo $redirectTo; ?>';
      </script>    



      <?php    
    }

        

    if ($userArea == "Cescom") 
    
    {
      
      ?>

      <script type="text/javascript">
      window.location = 'SoporteCalidad.php?usuario=<?php echo $userRut ?>';
       </script>

      <?php    
    }

    if ($userArea == "SOPORTE TQW") 
    
    {
      
      ?>

      <script type="text/javascript">
      window.location = 'Piloto_Soporte.php?usuario=<?php echo $userRut ?>';
       </script>

      <?php    
    }


    if ($userArea == "Supervisor") 
     
    {
      
        
        // Redirigir en función del perfil del usuario y agregar la clave temporal como parámetro GET
        $redirectTo = "new_dash.php?id_sesion=" . urlencode($clave_temporal);
        
        // $redirectTo = "Home_Super.php?id_sesion=" . urlencode($clave_temporal);
        ?>

        <script type="text/javascript">
          window.location = '<?php echo $redirectTo; ?>';
        </script>
      <?php    
    }

    else 
    {
      // Verificar si es un técnico
      if (strpos(strtolower($userArea), 'tecnico') !== false || strpos(strtolower($userProfile), 'tecnico') !== false) {
        // Establecer variables de sesión
        $_SESSION['periodo'] = "202505"; // Periodo estático
        $_SESSION['rut'] = $userRut;
        $_SESSION['id_sesion'] = $clave_temporal;
        
        $redirectTo = "activity_dashboard.php";
        ?>
        <script type="text/javascript">
          window.location = '<?php echo $redirectTo; ?>';
        </script>
        <?php
      } else {
        ?>
        <script type="text/javascript">
          <?php
            $redirectTo = "activity_dashboard.php?usuario=" . $userRut . "&id_sesion=" . urlencode($clave_temporal);
          ?>
          window.location.href = '<?php echo $redirectTo; ?>';
        </script>
        <?php    
      }
    }
}
    
else 
{
echo "<script>alert('Datos ingresados incorrectos');window.history.back();</script>";
}

?>
  

  <script>
  console.log("Variables en PHP:");
  <?php
    $variables = get_defined_vars();
    foreach ($variables as $name => $value) {
      if (!is_array($value) && !is_object($value)) {
        echo "console.log('$name: ' + '" . addslashes($value) . "');\n";
      }
    }
  ?>
</script>

    <!-- Main content -->
    
    <!-- /.content -->
  </body>
  




</html>
