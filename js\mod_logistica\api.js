/**
 * api.js
 * Funciones de comunicación con el servidor para el módulo de logística
 * Extraído del bloque JavaScript monolítico en mod_logistica.php
 */

// Función para cargar datos de una tabla específica
async function loadTableData(tableName) {
    // Verificar si la tabla específica está en mantenimiento
    if (window.ModLogisticaConfig && window.ModLogisticaConfig.maintenanceTables &&
        window.ModLogisticaConfig.maintenanceTables.includes(tableName)) {
        console.log(`🔧 Tabla ${tableName} en mantenimiento - No se cargarán datos`);
        return;
    }

    const tableCache = window.ModLogisticaConfig.tableCache;
    const loadingState = window.ModLogisticaConfig.loadingState;

    // Verificar si ya está cargando
    if (loadingState[tableName]) {
        console.log(`⏳ Tabla ${tableName} ya se está cargando...`);
        return;
    }

    // Verificar si ya está en cache
    if (tableCache[tableName]) {
        console.log(`📋 Usando datos en cache para tabla ${tableName}`);
        renderTableData(tableName, tableCache[tableName]);
        return;
    }

    // Marcar como cargando
    loadingState[tableName] = true;

    // Mostrar indicador de carga en el botón
    const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
    if (tabButton) {
        tabButton.classList.add('loading');
    }

    // Mostrar spinner en la tabla
    showTableLoading(tableName);

    try {
        // Preparar datos para envío POST
        const postData = new URLSearchParams();
        postData.append('tabla', tableName);
        postData.append('id_usuario', window.userId);

        // Usar el archivo real de carga de datos
        const response = await fetch('api_mod_logis_load_table.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: postData
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // Obtener el texto de la respuesta
        const responseText = await response.text();

        // Intentar limpiar la respuesta para extraer solo el JSON válido
        let cleanedResponse = responseText.trim();
        
        // Buscar el primer { y el último } para extraer solo el JSON
        const firstBrace = cleanedResponse.indexOf('{');
        const lastBrace = cleanedResponse.lastIndexOf('}');
        
        if (firstBrace !== -1 && lastBrace !== -1 && lastBrace > firstBrace) {
            cleanedResponse = cleanedResponse.substring(firstBrace, lastBrace + 1);
        }

        // Intentar parsear como JSON
        let data;
        try {
            data = JSON.parse(cleanedResponse);
        } catch (parseError) {
            console.error('❌ Error parseando JSON:', parseError);
            throw new Error(`Error parseando JSON: ${parseError.message}.`);
        }

        if (data.success) {
            // Guardar en cache
            tableCache[tableName] = data.datos;

            // Renderizar datos
            renderTableData(tableName, data.datos);

            // Marcar como cargado
            if (tabButton) {
                tabButton.classList.remove('loading');
                tabButton.classList.add('loaded');
            }
        } else {
            throw new Error(data.error || 'Error desconocido');
        }

    } catch (error) {
        console.error(`❌ Error cargando tabla ${tableName}:`, error);
        showTableError(tableName, error.message);

        // Remover indicador de carga
        if (tabButton) {
            tabButton.classList.remove('loading');
        }
    } finally {
        loadingState[tableName] = false;
    }
}

// Función para cargar historial directo
async function cargarHistorialDirecto(serie, id_orden) {
    console.log('📃 Cargando historial para serie:', serie);
    const webHistorialElement = document.getElementById('webHistorial');
    
    if (webHistorialElement) {
        // Mostrar indicador de carga
        webHistorialElement.innerHTML = `
            <div class="text-center p-3">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Cargando...</span>
                </div>
                <p class="mt-2">Cargando historial...</p>
            </div>
        `;
    }
    
    try {
        // Obtener precio - Primera llamada AJAX
        console.log('💰 Consultando precio para serie:', serie);
        const precioUrl = `GET_LOGISTICA.php?proceso=precio&serie=${encodeURIComponent(serie)}`;
        const precioResponse = await fetch(precioUrl);
        const precioText = await precioResponse.text();
        let precioData;
        
        try {
            // Intentar parsear la respuesta como JSON
            precioData = JSON.parse(precioText);
            console.log('💰 Precio obtenido:', precioData);
        } catch (parseError) {
            console.error('Error parseando respuesta de precio:', parseError);
            console.log('Respuesta cruda:', precioText);
            precioData = { precio: '0' };
        }
        
        // Asignar precio al elemento correcto
        const precioElement = document.getElementById('precioHistorial');
        if (precioElement) {
            precioElement.value = precioData.precio || '0';
        }

        // Obtener historial - Segunda llamada AJAX
        console.log('📖 Consultando historial para serie:', serie);
        const historialUrl = `GET_LOGISTICA.php?proceso=historial2&serie=${encodeURIComponent(serie)}`;
        const historialResponse = await fetch(historialUrl);
        
        if (!historialResponse.ok) {
            throw new Error(`Error HTTP: ${historialResponse.status}`);
        }
        
        const historialText = await historialResponse.text();
        let historialData;
        
        try {
            // Intentar parsear la respuesta como JSON
            historialData = JSON.parse(historialText);
            console.log('📖 Historial obtenido:', historialData);
        } catch (parseError) {
            console.error('Error parseando respuesta de historial:', parseError);
            console.log('Respuesta cruda:', historialText);
            throw new Error('Error parseando la respuesta del servidor');
        }
        
        // Renderizar historial en el elemento webHistorial con diseño mejorado que respeta iconos y texto
        if (webHistorialElement) {
            // Verificar si historialData es un array directamente (como muestra la consola)
            const dataArray = Array.isArray(historialData) ? historialData : 
                              (historialData.data ? historialData.data : []);
            
            if (dataArray.length > 0) {
                // DISEÑO CONTROLADO 100% POR CSS: historial-cards-inline.css
                // NO más estilos inline - todo se maneja desde archivos CSS externos
                
                // Construir contenido con diseño de tarjetas mejorado
                
                // Generar HTML simplificado para las tarjetas
                let cardHtml = '';
                
                dataArray.forEach((item, index) => {
                    // DEBUG: Log completo del item para verificar datos
                    console.log(`🔍 [DEBUG] Procesando item ${index + 1}:`, item);
                    console.log(`🔍 [DEBUG] archivo_adj:`, item.archivo_adj);
                    console.log(`🔍 [DEBUG] Tipo de archivo_adj:`, typeof item.archivo_adj);
                    console.log(`🔍 [DEBUG] archivo_adj es truthy?:`, !!item.archivo_adj);

                    // Extraer datos necesarios
                    const fecha = item.fecha_hora || item.fecha || 'Fecha no disponible';
                    const semantica = item.Semantica || item.estado || 'Estado no especificado';
                    const nombreOrigen = item.Nombre_origen || 'No especificado';
                    const nombreDestino = item.Nombre_destino || '';
                    const observacion = item.observacion || 'Sin observaciones';
                    
                    // Crear tarjeta usando clases CSS del archivo mod_logistica.css con etiquetas descriptivas
                    cardHtml += `
                    <div class="card-item">
                        <div class="card-content">
                            <div class="card-date">
                                <strong>Fecha:</strong> ${fecha}
                            </div>
                            
                            <p class="card-info">
                                <strong>Movimiento:</strong> ${semantica}
                            </p>
                            
                            <p class="card-info">
                                <strong>Técnico Origen:</strong> ${nombreOrigen}
                            </p>
                            
                            ${nombreDestino ? `
                            <p class="card-info">
                                <strong>Técnico Destino:</strong> ${nombreDestino}
                            </p>
                            ` : ''}
                            
                            <p class="card-info">
                                <strong>Observaciones:</strong> ${observacion}
                            </p>

                            ${(() => {
                                // DEBUG: Verificar archivo adjunto
                                console.log(`🔍 [DEBUG] Verificando archivo para item:`, item.archivo_adj);

                                if (item.archivo_adj && item.archivo_adj.trim() !== '') {
                                    console.log(`✅ [DEBUG] Archivo encontrado, generando HTML:`, item.archivo_adj);
                                    return `
                                    <p class="card-info archivo-info">
                                        📎 <strong>Archivo:</strong> <a href="${item.archivo_adj}" download class="btn btn-sm btn-outline-primary ms-2">
                                            <i class="bi bi-download"></i> Descargar respaldo
                                        </a>
                                    </p>`;
                                } else {
                                    console.log(`❌ [DEBUG] No se encontró archivo adjunto o está vacío`);
                                    return '';
                                }
                            })()}
                        </div>
                    </div>
                    `;
                });
                
                webHistorialElement.innerHTML = cardHtml;
            } else {
                // Mostrar mensaje cuando no hay historial usando clases CSS del archivo unificado
                webHistorialElement.innerHTML = `
                    <div class="alert-warning">
                        <i class="bi bi-info-circle me-2"></i>
                        No se encontró historial para esta serie.
                    </div>
                `;
            }
        }
        
        return historialData;
    } catch (error) {
        console.error('Error cargando historial:', error);
        
        // Mostrar error en la interfaz usando clases CSS del archivo unificado
        if (webHistorialElement) {
            webHistorialElement.innerHTML = `
                <div class="alert-danger">
                    <i class="bi bi-exclamation-triangle me-2"></i>
                    Error al cargar el historial: ${error.message}
                </div>
            `;
        }
        
        throw error;
    }
}

// Función para transferir registro usando XMLHttpRequest
function transferirRegistro(datos) {
    return new Promise((resolve, reject) => {
        const request = new XMLHttpRequest();
        request.timeout = 30000;

        request.onreadystatechange = function() {
            if (request.readyState === XMLHttpRequest.DONE) {
                if (request.status === 200) {
                    try {
                        const response = JSON.parse(request.responseText);
                        if (response.success) {
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error en la transferencia'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
            }
        };

        request.ontimeout = function() {
            reject(new Error('Tiempo de espera agotado'));
        };

        request.open('POST', 'GET_LOGIS_DIRECTA.php');
        request.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
        request.send(datos);
    });
}

// Función para enviar solicitud de aceptación
function enviarSolicitudAceptacion(formData) {
    return new Promise((resolve, reject) => {
        const request = new XMLHttpRequest();
        request.open('POST', 'GET_LOGIS_DIRECTA.php');
        
        request.onreadystatechange = function() {
            if (request.readyState === XMLHttpRequest.DONE) {
                if (request.status === 200) {
                    try {
                        const response = JSON.parse(request.responseText);
                        if (response.success) {
                            // Invalidar cache de la tabla para forzar recarga
                            invalidateTableCache('directa');
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error en la operación'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta'));
                    }
                } else {
                    reject(new Error('Error de conexión'));
                }
            }
        };
        
        request.send(formData);
    });
}

// Función para mantener sesión activa
function keepSessionAlive() {
    const xhr = new XMLHttpRequest();
    xhr.open('GET', 'session_ping.php?t=' + new Date().getTime(), true);
    xhr.send();
}

// Función para declarar entrega reversa
function declararEntregaReversa(formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'GET_LOGIS_DIRECTA.php');
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // Invalidar cache para forzar recarga de datos
                            invalidateTableCache('reversa');
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error en la operación'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
            }
        };
        
        xhr.send(formData);
    });
}

// Función para procesar transferencia reversa
function procesarTransferenciaReversa(formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'GET_LOGIS_DIRECTA.php');
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        resolve(response);
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
            }
        };
        
        xhr.send(formData);
    });
}

// Función para declarar instalación
function declararInstalacion(formData) {
    return new Promise((resolve, reject) => {
        const request = new XMLHttpRequest();
        request.open('POST', 'GET_LOGIS_DIRECTA.php');
        
        request.onreadystatechange = function() {
            if (request.readyState === XMLHttpRequest.DONE) {
                if (request.status === 200) {
                    try {
                        const response = JSON.parse(request.responseText);
                        if (response.success) {
                            // Invalidar cache para forzar recarga
                            invalidateTableCache('faltante');
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error en la instalación'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
            }
        };
        
        request.send(formData);
    });
}

// Función para rechazar material
function rechazarMaterial(formData) {
    return new Promise((resolve, reject) => {
        const xhr = new XMLHttpRequest();
        xhr.open('POST', 'GET_LOGIS_DIRECTA.php', true);
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.success) {
                            // Invalidar cache para forzar recarga
                            invalidateTableCache('recepcion');
                            resolve(response);
                        } else {
                            reject(new Error(response.message || 'Error al rechazar'));
                        }
                    } catch (e) {
                        reject(new Error('Error parseando respuesta del servidor'));
                    }
                } else {
                    reject(new Error('Error de conexión con el servidor'));
                }
            }
        };
        
        xhr.send(formData);
    });
}

// Función auxiliar para invalidar cache de tabla
function invalidateTableCache(tableName) {
    const tableCache = window.ModLogisticaConfig.tableCache;
    const loadingState = window.ModLogisticaConfig.loadingState;
    
    if (tableCache[tableName]) {
        console.log(`🗑️ Invalidando cache para tabla ${tableName}`);
        tableCache[tableName] = null;
        loadingState[tableName] = false;
        
        const tabButton = document.querySelector(`[data-tab="${tableName}"]`);
        if (tabButton) {
            tabButton.classList.remove('loaded');
        }
    }
}

// Exportar funciones al scope global para compatibilidad
window.ModLogisticaAPI = {
    loadTableData,
    cargarHistorialDirecto,
    transferirRegistro,
    enviarSolicitudAceptacion,
    keepSessionAlive,
    declararEntregaReversa,
    procesarTransferenciaReversa,
    declararInstalacion,
    rechazarMaterial,
    invalidateTableCache
};

// También exportar cargarHistorialDirecto directamente para compatibilidad con código existente
window.cargarHistorialDirecto = cargarHistorialDirecto;

// Log de inicialización
if (window.ModLogisticaConfig.debug) {
    console.log('🌐 API.js cargado - Funciones de comunicación con servidor inicializadas');
}