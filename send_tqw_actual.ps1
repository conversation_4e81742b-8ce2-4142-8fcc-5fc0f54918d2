param([int]$TimeframeHours = 3)

$SERVER = "servidor-tqw"
$PORT = "2277"
$DEST_BASE = "C:/wamp64/www/op_Tqw/APP_TQW/dist"

# Variables para estadísticas
$transferStats = @{
    TotalFiles = 0
    TotalSize = 0
    TotalDirectories = 0
    Errors = 0
    TransferredFiles = @()
}

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Yellow
Write-Host "                    TRANSFERENCIA DE ARCHIVOS TQW                            " -ForegroundColor Green
Write-Host "===============================================================================" -ForegroundColor Yellow
Write-Host ""

# Función para verificar conectividad
function Test-ServerConnection {
    Write-Host "Verificando conectividad con $SERVER..." -ForegroundColor Cyan
    $testResult = ssh -p $PORT $SERVER "echo 'Conexion exitosa'"
    if ($LASTEXITCODE -eq 0) {
        Write-Host "Conectividad verificada exitosamente" -ForegroundColor Green
        return $true
    } else {
        Write-Host "Error de conectividad con el servidor" -ForegroundColor Red
        return $false
    }
}

# Función para mostrar tabla de resumen
function Show-TransferSummary {
    Write-Host ""
    Write-Host "===============================================================================" -ForegroundColor Yellow
    Write-Host "                           RESUMEN DE TRANSFERENCIA                         " -ForegroundColor Green
    Write-Host "===============================================================================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "+-------------------------------------+---------------------------------------------+" -ForegroundColor Gray
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "METRICA                             " -NoNewline -ForegroundColor White
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "VALOR                                   " -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    Write-Host "+-------------------------------------+---------------------------------------------+" -ForegroundColor Gray
    
    $timeframeText = "$TimeframeHours horas"
    $serverText = "$SERVER`:$PORT"
    $dirsText = "$($transferStats.TotalDirectories)"
    $filesText = "$($transferStats.TotalFiles)"
    $sizeText = "{0:N2} KB" -f ($transferStats.TotalSize / 1KB)
    $errorsText = "$($transferStats.Errors)"
    
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "Timeframe                           " -NoNewline -ForegroundColor Cyan
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host ("{0,-39}" -f $timeframeText) -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "Servidor                            " -NoNewline -ForegroundColor Cyan
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host ("{0,-39}" -f $serverText) -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "Directorios procesados              " -NoNewline -ForegroundColor Cyan
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host ("{0,-39}" -f $dirsText) -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "Archivos transferidos               " -NoNewline -ForegroundColor Cyan
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host ("{0,-39}" -f $filesText) -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "Volumen total                       " -NoNewline -ForegroundColor Cyan
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host ("{0,-39}" -f $sizeText) -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "Errores                             " -NoNewline -ForegroundColor Cyan
    Write-Host "| " -NoNewline -ForegroundColor Gray
    $errorColor = if ($transferStats.Errors -eq 0) { "Green" } else { "Red" }
    Write-Host ("{0,-39}" -f $errorsText) -NoNewline -ForegroundColor $errorColor
    Write-Host "|" -ForegroundColor Gray
    
    Write-Host "+-------------------------------------+---------------------------------------------+" -ForegroundColor Gray
}

# Función para mostrar tabla de archivos transferidos
function Show-TransferredFiles {
    if ($transferStats.TransferredFiles.Count -eq 0) {
        Write-Host ""
        Write-Host "No se encontraron archivos para transferir" -ForegroundColor Yellow
        return
    }
    
    Write-Host ""
    Write-Host "===============================================================================" -ForegroundColor Yellow
    Write-Host "                        ARCHIVOS TRANSFERIDOS                               " -ForegroundColor Green
    Write-Host "===============================================================================" -ForegroundColor Yellow
    Write-Host ""
    
    Write-Host "+-------------------------+---------------------------------+----------+-----------+--------+" -ForegroundColor Gray
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "DIRECTORIO              " -NoNewline -ForegroundColor White
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "ARCHIVO                         " -NoNewline -ForegroundColor White
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "TAMAÑO   " -NoNewline -ForegroundColor White
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "VELOCIDAD " -NoNewline -ForegroundColor White
    Write-Host "| " -NoNewline -ForegroundColor Gray
    Write-Host "ESTADO " -NoNewline -ForegroundColor White
    Write-Host "|" -ForegroundColor Gray
    Write-Host "+-------------------------+---------------------------------+----------+-----------+--------+" -ForegroundColor Gray
    
    foreach ($file in $transferStats.TransferredFiles) {
        $dirDisplay = if ($file.Directory.Length -gt 22) { $file.Directory.Substring(0, 19) + "..." } else { $file.Directory }
        $fileDisplay = if ($file.FileName.Length -gt 30) { $file.FileName.Substring(0, 27) + "..." } else { $file.FileName }
        $sizeDisplay = "{0:N1}KB" -f ($file.Size / 1KB)
        $speedDisplay = $file.Speed
        $statusDisplay = if ($file.Status -eq "OK") { "OK" } else { "ERROR" }
        $statusColor = if ($file.Status -eq "OK") { "Green" } else { "Red" }
        
        Write-Host "| " -NoNewline -ForegroundColor Gray
        Write-Host ("{0,-23}" -f $dirDisplay) -NoNewline -ForegroundColor Cyan
        Write-Host "| " -NoNewline -ForegroundColor Gray
        Write-Host ("{0,-31}" -f $fileDisplay) -NoNewline -ForegroundColor White
        Write-Host "| " -NoNewline -ForegroundColor Gray
        Write-Host ("{0,8}" -f $sizeDisplay) -NoNewline -ForegroundColor Yellow
        Write-Host "| " -NoNewline -ForegroundColor Gray
        Write-Host ("{0,9}" -f $speedDisplay) -NoNewline -ForegroundColor Magenta
        Write-Host "| " -NoNewline -ForegroundColor Gray
        Write-Host ("{0,6}" -f $statusDisplay) -NoNewline -ForegroundColor $statusColor
        Write-Host "|" -ForegroundColor Gray
    }
    
    Write-Host "+-------------------------+---------------------------------+----------+-----------+--------+" -ForegroundColor Gray
}

# Verificar conectividad antes de proceder
if (-not (Test-ServerConnection)) {
    Write-Host "No se puede continuar sin conectividad al servidor" -ForegroundColor Red
    exit 1
}

$timeLimit = (Get-Date).AddHours(-$TimeframeHours)

$directoriesToCheck = @(
    @{ Path = "."; Filter = "*.php"; Dest = "$DEST_BASE"; Name = "Raiz (.)" },
    @{ Path = "."; Filter = "*.html"; Dest = "$DEST_BASE"; Name = "Raiz (.)" },
    @{ Path = "js"; Filter = "*.*"; Dest = "$DEST_BASE\js"; Name = "js/" },
    @{ Path = "css"; Filter = "*.*"; Dest = "$DEST_BASE\css"; Name = "css/" },
    @{ Path = "components"; Filter = "*.*"; Dest = "$DEST_BASE\components"; Name = "components/" },
    @{ Path = "includes"; Filter = "*.*"; Dest = "$DEST_BASE\includes"; Name = "includes/" },
    @{ Path = "js\modules"; Filter = "*.*"; Dest = "$DEST_BASE\js\modules"; Name = "js/modules/" },
    @{ Path = "js\modules\calidadReactiva"; Filter = "*.*"; Dest = "$DEST_BASE\js\modules\calidadReactiva"; Name = "js/modules/calidadReactiva/" },
    @{ Path = "js\mod_logistica"; Filter = "*.*"; Dest = "$DEST_BASE\js\mod_logistica"; Name = "js/mod_logistica/" },
    @{ Path = "config"; Filter = "*.*"; Dest = "$DEST_BASE\config"; Name = "config/" }
)

Write-Host ""
Write-Host "Iniciando proceso de transferencia..." -ForegroundColor Green
Write-Host ""

foreach ($dir in $directoriesToCheck) {
    if (Test-Path $dir.Path) {
        $recentFiles = Get-ChildItem -Path $dir.Path -Filter $dir.Filter -File | Where-Object { $_.LastWriteTime -gt $timeLimit }

        if ($recentFiles.Count -gt 0) {
            $transferStats.TotalDirectories++
            Write-Host "Procesando $($dir.Name)..." -ForegroundColor Yellow

            # Crear directorio remoto si no existe
            $createCmd = "if not exist `"$($dir.Dest)`" mkdir `"$($dir.Dest)`""
            ssh -p $PORT $SERVER "cmd /c `"$createCmd`"" | Out-Null

            foreach ($file in $recentFiles) {
                Write-Host "  Transfiriendo $($file.Name)..." -ForegroundColor Cyan -NoNewline

                # Convertir ruta para SCP (usar forward slashes)
                $scpDest = $dir.Dest -replace "\\", "/"
                
                # Medir tiempo de transferencia
                $startTime = Get-Date
                scp -P $PORT $file.FullName "${SERVER}:$scpDest/" 2>$null
                $endTime = Get-Date
                $duration = ($endTime - $startTime).TotalSeconds
                $speed = if ($duration -gt 0) { "{0:N1}KB/s" -f (($file.Length / 1KB) / $duration) } else { "N/A" }

                if ($LASTEXITCODE -eq 0) {
                    Write-Host " OK" -ForegroundColor Green
                    $transferStats.TotalFiles++
                    $transferStats.TotalSize += $file.Length
                    $transferStats.TransferredFiles += @{
                        Directory = $dir.Name
                        FileName = $file.Name
                        Size = $file.Length
                        Speed = $speed
                        Status = "OK"
                    }
                } else {
                    Write-Host " ERROR" -ForegroundColor Red
                    $transferStats.Errors++
                    $transferStats.TransferredFiles += @{
                        Directory = $dir.Name
                        FileName = $file.Name
                        Size = $file.Length
                        Speed = "N/A"
                        Status = "ERROR"
                    }
                }
            }
        }
    }
}

# Mostrar resultados en formato tabla
Show-TransferredFiles
Show-TransferSummary

Write-Host ""
Write-Host "===============================================================================" -ForegroundColor Yellow
Write-Host "                           TRANSFERENCIA COMPLETADA                         " -ForegroundColor Green
Write-Host "===============================================================================" -ForegroundColor Yellow
Write-Host ""