/**
 * main.js
 * Punto de entrada principal para el módulo de logística
 * Inicializa todos los módulos en el orden correcto
 */

// Función principal de inicialización
function initModuloLogistica() {
    console.log('🚀 Iniciando módulo de logística...');

    // Verificar si está en modo mantenimiento
    if (window.ModLogisticaConfig && window.ModLogisticaConfig.maintenanceMode) {
        console.log('🔧 Modo mantenimiento activado - No se cargarán datos de tablas');
        return;
    }

    // Verificar que todas las dependencias estén cargadas
    if (!window.ModLogisticaConfig) {
        console.error('❌ ModLogisticaConfig no está disponible');
        return;
    }
    
    if (!window.ModLogisticaAPI) {
        console.error('❌ ModLogisticaAPI no está disponible');
        return;
    }
    
    if (!window.ModLogisticaUI) {
        console.error('❌ ModLogisticaUI no está disponible');
        return;
    }
    
    if (!window.ModLogisticaTables) {
        console.error('❌ ModLogisticaTables no está disponible');
        return;
    }
    
    if (!window.ModLogisticaHandlers) {
        console.error('❌ ModLogisticaHandlers no está disponible');
        return;
    }
    
    try {
        // 1. Verificar configuración de usuario
        if (!window.userId) {
            console.warn('⚠️ userId no está definido');
        }
        
        // 2. Inicializar sistema de pestañas
        console.log('🔧 Inicializando sistema de pestañas...');
        initTabSystem();
        
        // 3. Inicializar sistema de búsqueda
        console.log('🔍 Inicializando sistema de búsqueda...');
        initSearchSystem();
        
        // 4. Inicializar handlers de formularios
        console.log('📝 Inicializando handlers de formularios...');
        if (typeof inicializarHandlersFormularios === 'function') {
            inicializarHandlersFormularios();
        } else {
            console.warn('⚠️ inicializarHandlersFormularios no está disponible');
        }
        
        // 5. Inicializar validación en tiempo real
        console.log('✅ Inicializando validación en tiempo real...');
        if (typeof inicializarValidacionTiempoReal === 'function') {
            inicializarValidacionTiempoReal();
        } else {
            console.warn('⚠️ inicializarValidacionTiempoReal no está disponible');
        }
        
        // 6. Cargar datos iniciales de la tabla activa (recepción)
        console.log('📋 Cargando datos iniciales de recepción...');
        setTimeout(() => {
            loadTableData('recepcion');
        }, 100);
        
        // 7. Configurar sistema de mantener sesión activa (cada 5 minutos)
        console.log('⏱️ Configurando sistema de mantener sesión activa...');
        setInterval(() => {
            keepSessionAlive();
        }, 5 * 60 * 1000); // 5 minutos
        
        // 8. Configurar manejo global de errores para el módulo
        setupGlobalErrorHandling();
        
        // 9. Configurar limpieza cuando se cierre la página
        setupPageUnloadHandling();
        
        console.log('✅ Módulo de logística inicializado correctamente');
        
        // Notificar que el módulo está listo
        if (window.ModLogisticaConfig.debug) {
            mostrarNotificacion('Sistema de logística cargado', 'success');
        }
        
    } catch (error) {
        console.error('❌ Error inicializando módulo de logística:', error);
        mostrarNotificacion('Error al inicializar el sistema de logística', 'danger');
    }
}

// Función para configurar manejo global de errores
function setupGlobalErrorHandling() {
    // Capturar errores JavaScript no manejados
    window.addEventListener('error', function(event) {
        if (event.filename && event.filename.includes('mod_logistica')) {
            console.error('❌ Error no manejado en módulo de logística:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
            
            if (window.ModLogisticaConfig && window.ModLogisticaConfig.debug) {
                mostrarNotificacion('Error detectado en sistema de logística', 'warning');
            }
        }
    });
    
    // Capturar promesas rechazadas no manejadas
    window.addEventListener('unhandledrejection', function(event) {
        console.error('❌ Promesa rechazada no manejada en módulo de logística:', event.reason);
        
        if (window.ModLogisticaConfig && window.ModLogisticaConfig.debug) {
            mostrarNotificacion('Error de comunicación detectado', 'warning');
        }
    });
}

// Función para configurar manejo cuando se cierre la página
function setupPageUnloadHandling() {
    window.addEventListener('beforeunload', function() {
        // Cerrar cualquier offcanvas abierto
        document.querySelectorAll('.offcanvas.show').forEach(offcanvasEl => {
            const instance = bootstrap.Offcanvas.getInstance(offcanvasEl);
            if (instance) {
                try {
                    instance.hide();
                } catch (e) {
                    console.warn('Error al cerrar offcanvas en beforeunload:', e);
                }
            }
        });
        
        // Limpiar modal-open del body
        document.body.classList.remove('modal-open');
        
        console.log('🧹 Limpieza realizada antes de cerrar página');
    });
}

// Función para verificar estado del sistema
function checkSystemStatus() {
    const status = {
        config: !!window.ModLogisticaConfig,
        api: !!window.ModLogisticaAPI,
        ui: !!window.ModLogisticaUI,
        tables: !!window.ModLogisticaTables,
        handlers: !!window.ModLogisticaHandlers,
        userId: !!window.userId,
        bootstrap: !!window.bootstrap
    };
    
    console.log('📊 Estado del sistema:', status);
    return status;
}

// Función para reinicializar el módulo si es necesario
function reinitializeModule() {
    console.log('🔄 Reinicializando módulo de logística...');
    
    // Limpiar cache
    if (window.ModLogisticaConfig && window.ModLogisticaConfig.tableCache) {
        Object.keys(window.ModLogisticaConfig.tableCache).forEach(table => {
            window.ModLogisticaConfig.tableCache[table] = null;
        });
    }
    
    // Limpiar estado de carga
    if (window.ModLogisticaConfig && window.ModLogisticaConfig.loadingState) {
        Object.keys(window.ModLogisticaConfig.loadingState).forEach(table => {
            window.ModLogisticaConfig.loadingState[table] = false;
        });
    }
    
    // Reinicializar
    initModuloLogistica();
}

// Función para obtener estadísticas del módulo
function getModuleStats() {
    const stats = {
        timestamp: new Date().toISOString(),
        userAgent: navigator.userAgent,
        url: window.location.href,
        userId: window.userId,
        config: window.ModLogisticaConfig,
        cacheStatus: {}
    };
    
    if (window.ModLogisticaConfig && window.ModLogisticaConfig.tableCache) {
        Object.keys(window.ModLogisticaConfig.tableCache).forEach(table => {
            stats.cacheStatus[table] = {
                hasData: !!window.ModLogisticaConfig.tableCache[table],
                dataCount: window.ModLogisticaConfig.tableCache[table] ? 
                    window.ModLogisticaConfig.tableCache[table].length : 0,
                isLoading: window.ModLogisticaConfig.loadingState[table] || false
            };
        });
    }
    
    return stats;
}

// Esperar a que el DOM esté completamente cargado
document.addEventListener('DOMContentLoaded', function() {
    console.log('📄 DOM cargado, esperando inicialización del módulo de logística...');
    
    // Esperar un poco más para asegurar que todos los módulos estén cargados
    setTimeout(() => {
        initModuloLogistica();
    }, 100);
});

// Exportar funciones útiles al scope global
window.ModLogisticaMain = {
    init: initModuloLogistica,
    checkStatus: checkSystemStatus,
    reinitialize: reinitializeModule,
    getStats: getModuleStats,
    setupGlobalErrorHandling,
    setupPageUnloadHandling
};

// Compatibilidad con funciones globales existentes
window.initModuloLogistica = initModuloLogistica;
window.checkSystemStatus = checkSystemStatus;
window.reinitializeModule = reinitializeModule;

// Log de carga del módulo principal
console.log('📋 Main.js cargado - Punto de entrada del módulo de logística listo');