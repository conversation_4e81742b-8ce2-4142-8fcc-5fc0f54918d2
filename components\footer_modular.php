<?php
/**
 * Footer modular para manejo de formularios dinámicos
 * Versión simplificada y escalable
 */

$currentPage = basename($_SERVER['PHP_SELF']);

// Definir los IDs de los top 10 técnicos que deben ser redirigidos a mod_logistica.php
$top_technicians = [1152, 1312, 233, 1218, 1153, 1223, 1246, 1150, 1301, 1211];

// Determinar la URL del tecnico_home basado en si el usuario es un top technician
$tecnico_home_url = 'Tecnico_Home_LOGIS_TEST_V2.php';
if (isset($id_usuario) && in_array($id_usuario, $top_technicians)) {
    $tecnico_home_url = 'mod_logistica.php';
}

// Configuración de formularios disponibles
$formularios = [
    [
        'id' => 'materiales',
        'icono' => 'bi-box-seam',
        'titulo' => 'Solicitud de Materiales',
        'archivo' => 'forms/form_materiales.php',
        'script' => 'js/forms/form_materiales_cascada_simple.js'
    ],
    [
        'id' => 'revision',
        'icono' => 'bi-clipboard-check',
        'titulo' => 'Formulario de Revisión',
        'archivo' => 'forms/form_revision.php',
        'script' => 'js/forms/form_revision.js',
        'oculto' => true
    ],
    [
        'id' => 'soporte',
        'icono' => 'bi-headset',
        'titulo' => 'Solicitud de Soporte',
        'archivo' => 'forms/form_soporte.php',
        'script' => 'js/forms/form_soporte.js',
        'oculto' => true
    ]
];

// Obtener conexión a la base de datos si es necesaria
if (!isset($conex) && file_exists(__DIR__ . '/../DatabaseConnection.php')) {
    try {
        require_once __DIR__ . '/../DatabaseConnection.php';
        $db = DatabaseConnection::getInstance();
        $conex = $db->getConnection();
    } catch (Exception $e) {
        // Error silencioso
    }
}
?>

<!-- Estilos base del footer modular -->
<link rel="stylesheet" href="css/footer_modular.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/form_panel.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/floating_menu.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/form_select_normal.css?v=<?php echo time(); ?>">
<link rel="stylesheet" href="css/searchable_select.css?v=<?php echo time(); ?>">

<!-- Navegación inferior -->
<nav class="bottom-nav" id="bottom-nav">
    <!-- Iconos a la izquierda -->
    <a href="activity_dashboard.php" class="nav-item <?php echo ($currentPage == 'activity_dashboard.php') ? 'active' : ''; ?>">
        <i class="bi bi-grid"></i>
    </a>
    <a href="charts_dashboard.php" class="nav-item <?php echo ($currentPage == 'charts_dashboard.php') ? 'active' : ''; ?>">
        <i class="bi bi-activity"></i>
    </a>
    <a href="calidad_reactiva.php" class="nav-item <?php echo ($currentPage == 'calidad_reactiva.php') ? 'active' : ''; ?>">
        <i class="bi bi-file-text"></i>
    </a>

    <!-- Botón + central -->
    <div class="add-button-container">
        <div class="floating-menu" id="floatingMenu">
            <?php foreach ($formularios as $form): ?>
                <?php if ($form['id'] === 'materiales'): ?>
                <a href="#" class="floating-menu-item" data-form-id="<?php echo $form['id']; ?>">
                    <i class="bi <?php echo $form['icono']; ?>"></i> <?php echo $form['titulo']; ?>
                </a>
                <?php endif; ?>
            <?php endforeach; ?>
        </div>
        <div class="nav-item add-button" id="addButton">
            <i class="bi bi-plus"></i>
        </div>
    </div>

    <!-- Iconos a la derecha -->
    <a href="javascript:void(0)" class="nav-item" id="ticketButton">
        <i class="bi bi-ticket"></i>
    </a>
    <a href="<?php echo $tecnico_home_url; ?>" class="nav-item <?php echo ($currentPage == 'Tecnico_Home_LOGIS_TEST_V2.php' || $currentPage == 'mod_logistica.php') ? 'active' : ''; ?>">
        <i class="bi bi-box"></i>
    </a>
    <a href="#" class="nav-item" id="logoutButton">
        <i class="bi bi-box-arrow-right"></i>
    </a>
</nav>

<!-- Modal de Cierre de Sesión -->
<div id="logoutModal" class="logout-modal">
  <div class="logout-modal-content">
    <div class="logout-modal-header">
      <h2>Confirmar Cierre de Sesión</h2>
    </div>
    <div class="logout-modal-body">
      <p>¿Estás seguro de que quieres cerrar la sesión?</p>
    </div>
    <div class="logout-modal-footer">
      <button id="cancelLogout" class="btn-logout-cancel">Cancelar</button>
      <a href="logout.php" id="confirmLogout" class="btn-logout-confirm">Cerrar Sesión</a>
    </div>
  </div>
</div>

<!-- Overlays -->
<div class="menu-overlay" id="menuOverlay"></div>
<div class="form-overlay" id="formOverlay"></div>

<!-- Contenedor de formularios dinámicos -->
<div id="formsContainer">
    <?php foreach ($formularios as $form): ?>
        <div class="form-panel" id="form-<?php echo $form['id']; ?>" data-form-id="<?php echo $form['id']; ?>">
            <?php 
                // Incluir el archivo del formulario si existe
                $formPath = __DIR__ . '/../' . $form['archivo'];
                if (file_exists($formPath)) {
                    include $formPath;
                } else {
                    echo '<div class="form-placeholder">Formulario no encontrado: ' . $form['archivo'] . '</div>';
                }
            ?>
        </div>
    <?php endforeach; ?>
</div>

<!-- Scripts del sistema modular -->
<script src="js/core/footer_modular.js"></script>

<!-- Cargar scripts específicos de cada formulario -->
<?php foreach ($formularios as $form): ?>
    <?php if (isset($form['script'])): ?>
        <script src="<?php echo $form['script']; ?>"></script>
    <?php endif; ?>
<?php endforeach; ?>

<!-- Configuración de formularios disponibles para JavaScript -->
<script>
    window.FORMULARIOS_CONFIG = <?php echo json_encode($formularios); ?>;
</script>